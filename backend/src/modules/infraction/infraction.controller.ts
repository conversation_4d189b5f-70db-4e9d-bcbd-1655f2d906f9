import { Controller, Get, Post, Body, Patch, Param, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { InfractionService } from './infraction.service';
import { CreateInfractionDto } from './dto/create-infraction.dto';
import { UpdateInfractionDto } from './dto/update-infraction.dto';
import { ProtectedRoute } from '../../common';
import { Context } from '../../common/decorators/context.decorator';
import { Me } from '../../common/decorators/me.decorator';
import { InfractionCreateService } from './service/infraction-create.service';
import { InfractionGetService } from './service/infraction-get.service';

@Controller('infraction')
export class InfractionController {
  constructor(
    private readonly infractionService: InfractionService,
    private readonly infractionCreateService: InfractionCreateService,
    private readonly infractionGetService: InfractionGetService,
  ) {}


  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Post()
  create(@Me('id') userId: string, @Body() createInfractionDto: CreateInfractionDto) {
    return this.infractionCreateService.manualCreateInfractionToUser(userId, createInfractionDto);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get()
  findAll(@Context('organizationId') orgId: string, @Query('page') page?: string, @Query('limit') limit?: string) {
    return this.infractionGetService.getAllInfractions(orgId, page, limit);
  }
  
  @ProtectedRoute({
    isPublic: false,
    context: 'PERSONAL',
  })
  @Get('/mains')
  async getMyInfraction(@Me('id') userId: string) {
    return this.infractionGetService.getByUserId(userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.infractionService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateInfractionDto: UpdateInfractionDto) {
    return this.infractionService.update(+id, updateInfractionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.infractionService.remove(+id);
  }
}
