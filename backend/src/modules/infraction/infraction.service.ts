import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { CreateInfractionDto } from './dto/create-infraction.dto';
import { UpdateInfractionDto } from './dto/update-infraction.dto';
import { PrismaService } from '../../common';
import { Prisma } from '@prisma/client';

@Injectable()
export class InfractionService {
  constructor(private prismaService: PrismaService) {}

  async create(createInfractionDto: CreateInfractionDto) {
    try {
      const infraction = await this.prismaService.infraction.create({
        data: {
          name: createInfractionDto.name,
          description: createInfractionDto.description,
          userId: createInfractionDto.userId,
          data: createInfractionDto.data,
          infractionDate: createInfractionDto.infractionDate,
        },
      });

      return infraction;
    } catch (error) {
      throw new InternalServerErrorException('Error creating infraction');
    }
  }

  async findAll(where?: Prisma.InfractionWhereInput, select?: Prisma.InfractionSelect) {
    return this.prismaService.infraction.findMany({
      where,
      select,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    try {
      const infraction = await this.prismaService.infraction.findUnique({
        where: { id },
        select: {
          InfractionReason: {
            select: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
              file: {
                select: {
                  id: true,
                  path: true,
                },
              },
            },
          },
        },
      });
      return infraction;
    } catch (error) {
      throw new InternalServerErrorException(`Infraction with id ${id} not found`);
    }
  }

  update(id: number, updateInfractionDto: UpdateInfractionDto) {
    return `This action updates a #${id} infraction`;
  }

  remove(id: number) {
    return `This action removes a #${id} infraction`;
  }
}
