import { axiosPrivate } from '@/config/api'
import { infractionEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { ApiResponse } from '../types'

export interface TInfractionReason {
	id: string
	fileId?: string
	name?: string
	description?: string
	infractionId: string
	createdAt: string
	updatedAt: string
	file?: {
		id: string
		path: string
		name: string
	}
}

export interface TInfraction {
	id: string
	userId: string
	name?: string
	description?: string
	data?: any
	infractionDate: string
	createdAt: string
	updatedAt: string
	user: {
		id: string
		fullName: string
		phone: string
		avatar?: {
			path: string
		}
		organization?: {
			id: string
			name: string
		}
		Organization?: Array<{
			name: string
		}>
		MainOrganization?: {
			name: string
		} | null
		position?: {
			id: string
			name: string
		}
	}
	InfractionReason: TInfractionReason[]
}

export type TInfractionResponse = ApiResponse<TInfraction[]>

/**
 * Hook to get all infractions for organization workspace
 * This fetches all infractions in the organization
 */
export const useGetAllInfractions = () => {
	return useQuery({
		queryKey: ['infractions', 'all'],
		queryFn: async (): Promise<TInfractionResponse> => {
			const response = await axiosPrivate.get<TInfractionResponse>(infractionEndpoints.all)
			return response.data
		}
	})
}

/**
 * Hook to get personal infractions for personal workspace
 * This fetches infractions assigned to the current user
 */
export const useGetMyInfractions = () => {
	return useQuery({
		queryKey: ['infractions', 'mine'],
		queryFn: async (): Promise<TInfractionResponse> => {
			const response = await axiosPrivate.get<TInfractionResponse>(infractionEndpoints.me)
			return response.data
		}
	})
}

/**
 * Hook to get infractions based on workspace context
 * @param workspace - 'personal' for my infractions, 'organization' for all infractions
 */
export const useGetInfractions = (workspace: 'personal' | 'organization') => {
	return useQuery({
		queryKey: ['infractions', workspace],
		queryFn: async (): Promise<TInfractionResponse> => {
			const endpoint = workspace === 'personal' ? infractionEndpoints.me : infractionEndpoints.all
			const response = await axiosPrivate.get<TInfractionResponse>(endpoint)
			return response.data
		}
	})
}

/**
 * Hook to get a single infraction by ID
 */
export const useGetInfractionById = (id: string) => {
	return useQuery({
		queryKey: ['infraction', id],
		queryFn: async (): Promise<ApiResponse<TInfraction>> => {
			const response = await axiosPrivate.get<ApiResponse<TInfraction>>(infractionEndpoints.one(id))
			return response.data
		},
		enabled: !!id
	})
}
