import { Table, Card, Typography,} from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { usePagination } from '@/shared/hooks/usePagination'
import { TInfraction, useGetInfractions } from '@/config/queries/infraction/get-all.queries'

const { Text } = Typography

const InfractionTable = () => {
	const navigate = useNavigate()
	const location = useLocation()
	const workspace = location.pathname.includes('/personal') ? 'personal' : 'organization'

	const { pagination, handlePaginationChange } = usePagination(10)

	const { data: infractionData, isLoading } = useGetInfractions(workspace)

	const handleRowClick = (record: TInfraction) => {
		navigate(`/infraction/${record.id}`)
	}

	// Define columns based on workspace
	const columns = workspace === 'personal'
		? [
			{
				title: 'ID',
				key: 'index',
				width: 60,
				render: (_: any, __: any, index: number) => (
					<span className='text-gray-500'>
						{formatTableIndex(pagination.current, pagination.pageSize, index)}
					</span>
				)
			},
			{
				title: 'Qoidabuzarlik nomi',
				dataIndex: 'name',
				key: 'name',
				width: 150,
				className: 'text-start truncate max-w-24',
				render: (name: string) => name || '-'
			},
			{
				title: 'Tavsif',
				dataIndex: 'description',
				key: 'description',
				width: 200,
				className: 'text-start truncate max-w-24',
				render: (description: string) => description || '-'
			},
			{
				title: 'Qoidabuzarlik sanasi',
				dataIndex: 'infractionDate',
				key: 'infractionDate',
				width: 150,
				render: (date: string) =>
					dayjs(date).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')
			}
		]
		: [
			{
				title: 'ID',
				key: 'index',
				width: 60,
				render: (_: any, __: any, index: number) => (
					<span className='text-gray-500'>
						{formatTableIndex(pagination.current, pagination.pageSize, index)}
					</span>
				)
			},
			{
				title: 'FIO',
				key: 'fullName',
				width: 150,
				className: 'text-start truncate max-w-24',
				render: (record: TInfraction) => (
					<Text>{record.user?.fullName || '-'}</Text>
				)
			},
			{
				title: 'Qoidabuzarlik nomi',
				dataIndex: 'name',
				key: 'name',
				width: 150,
				className: 'text-start truncate max-w-24',
				render: (name: string) => name || '-'
			},
			{
				title: 'Tavsif',
				dataIndex: 'description',
				key: 'description',
				width: 200,
				className: 'text-start truncate max-w-24',
				render: (description: string) => description || '-'
			},
			{
				title: 'Tashkilot',
				key: 'organization',
				width: 200,
				className: 'text-start truncate max-w-32',
				render: (record: TInfraction) => {
					const organizations: string[] = []

					// Add MainOrganization if exists
					if (record.user?.MainOrganization?.name) {
						organizations.push(record.user.MainOrganization.name)
					}

					// Add all organizations from Organization array
					if (record.user?.Organization && record.user.Organization.length > 0) {
						record.user.Organization.forEach(org => {
							if (org.name && !organizations.includes(org.name)) {
								organizations.push(org.name)
							}
						})
					}

					return (
						<Text>
							{organizations.length > 0 ? organizations.join(', ') : '-'}
						</Text>
					)
				}
			},
			{
				title: 'Lavozim',
				key: 'position',
				width: 150,
				className: 'text-start truncate max-w-24',
				render: (record: TInfraction) => (
					<Text>{record.user?.position?.name || '-'}</Text>
				)
			},
			{
				title: 'Qoidabuzarlik sanasi',
				dataIndex: 'infractionDate',
				key: 'infractionDate',
				width: 150,
				render: (date: string) =>
					dayjs(date).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')
			}
		]

	return (
		<Card className='p-4'>
			<div className='p-3 rounded-lg shadow-sm dark:bg-primary'>
				<Table
					title={() => (
						<div className='flex justify-between items-center'>
							<Search />
						</div>
					)}
					loading={isLoading}
					columns={columns}
					scroll={{ x: 'max-content' }}
					dataSource={infractionData?.data || []}
					rowKey='id'
					size='small'
					pagination={{
						...pagination,
						showTotal: (total) => `Jami ${total} ta`,
						showSizeChanger: true,
						pageSizeOptions: [10, 20, 50, 100]
					}}
					onChange={handlePaginationChange}
					onRow={(record: TInfraction) => ({
						onClick: () => handleRowClick(record),
						style: { cursor: 'pointer' }
					})}
					locale={{
						emptyText: workspace === 'personal'
							? "Qoidabuzarliklaringiz topilmadi"
							: "Qoidabuzarliklar topilmadi"
					}}
				/>
			</div>
		</Card>
	)
}

export default InfractionTable