import { Table, Card, Typography,} from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { usePagination } from '@/shared/hooks/usePagination'
import { TInfraction, useGetInfractions } from '@/config/queries/infraction/get-all.queries'

const { Text } = Typography

const InfractionTable = () => {
	const navigate = useNavigate()
	// Determine workspace based on current path
	const location = useLocation()
	const workspace = location.pathname.includes('/personal') ? 'personal' : 'organization'

	// Use pagination hook
	const { pagination, handlePaginationChange } = usePagination(10)

	// Use the appropriate query based on workspace
	const { data: infractionData, isLoading } = useGetInfractions(workspace)

	const handleRowClick = (record: TInfraction) => {
		// Navigate to infraction details page
		navigate(`/infraction/${record.id}`)
	}

	const columns = [
		{
			title: 'ID',
			key: 'index',
			width: 60,
			render: (_: any, __: any, index: number) => (
				<span className='text-gray-500'>
					{formatTableIndex(pagination.current, pagination.pageSize, index)}
				</span>
			)
		},
		{
			title: 'FIO',
			dataIndex: 'fullName',
			key: 'fullName',
			width: 150,
			className: 'text-start truncate max-w-24',
			render: (fullName: string) => fullName || '-'
		},
		{
			title: 'Qoidabuzarlik nomi',
			dataIndex: 'name',
			key: 'name',
			width: 150,
			className: 'text-start truncate max-w-24',
			render: (name: string) => name || '-'
		},
		{
			title: 'Tavsif',
			dataIndex: 'description',
			key: 'description',
			width: 200,
			className: 'text-start truncate max-w-24',
			render: (description: string) => description || '-'
		},
		{
			title: 'Tashkilot',
			key: 'organization',
			width: 150,
			className: 'text-start truncate max-w-24',
			render: (record: TInfraction) => (
				<Text>{record.user?.organization?.name || '-'}</Text>
			)
		},
		{
			title: 'Lavozim',
			key: 'position',
			width: 150,
			className: 'text-start truncate max-w-24',
			render: (record: TInfraction) => (
				<Text>{record.user?.position?.name || '-'}</Text>
			)
		},
		{
			title: 'Qoidabuzarlik sanasi',
			dataIndex: 'infractionDate',
			key: 'infractionDate',
			width: 150,
			render: (date: string) =>
				dayjs(date).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')
		},
	]

	return (
		<Card className='p-4'>
			<div className='p-3 rounded-lg shadow-sm dark:bg-primary'>
				<Table
					title={() => (
						<div className='flex justify-between items-center'>
							<Search />
						</div>
					)}
					loading={isLoading}
					columns={columns}
					scroll={{ x: 'max-content' }}
					dataSource={infractionData?.data || []}
					rowKey='id'
					size='small'
					pagination={{
						...pagination,
						showTotal: (total) => `Jami ${total} ta`,
						showSizeChanger: true,
						pageSizeOptions: [10, 20, 50, 100]
					}}
					onChange={handlePaginationChange}
					onRow={(record: TInfraction) => ({
						onClick: () => handleRowClick(record),
						style: { cursor: 'pointer' }
					})}
					locale={{
						emptyText: workspace === 'personal'
							? "Qoidabuzarliklaringiz topilmadi"
							: "Qoidabuzarliklar topilmadi"
					}}
				/>
			</div>
		</Card>
	)
}

export default InfractionTable